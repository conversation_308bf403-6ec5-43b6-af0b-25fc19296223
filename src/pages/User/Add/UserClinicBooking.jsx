import React, { useState, useEffect, useContext, useMemo } from "react";
import BottomDrawer from "Components/Drawers/BottomDrawer";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import SelectionOption from "Components/SelectionOption";
import { format } from "date-fns";
import { BackButton } from "Components/BackButton";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import { Link, useParams, useLocation, useNavigate } from "react-router-dom";
import TreeSDK from "Utils/TreeSDK";
import LoadingSpinner from "Components/LoadingSpinner";
import {
  activityLogTypes,
  eventTypeOptions,
  generateTimeSlots,
  reservationTypes,
  updateBrowserTab,
  calculateClinicFees,
  actionLogTypes,
} from "Utils/utils";
import TimeSlots from "Components/TimeSlots/TimeSlots";
import { getManyByIds } from "Context/Global";
import { AuthContext } from "Context/Auth";
import AddPlayers from "Components/Players/AddPlayers";
import { calculateServiceFee } from "Utils/utils";
import { ClinicReservationSummary } from "Components/Reservation/ReservationSummary";
import CheckoutForm from "Components/PaymentForm";
import { useClub } from "Context/Club";
import WarningModal from "Components/Modals/WarningModal";
import Select from "react-select";
import AccessRestricted from "Components/Shared/AccessRestricted";
import { getCustomRequestThreshold } from "Utils/customThresholdUtils";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const ClinicBooking = ({
  isOpen = true,
  onClose = () => {},
  surfaces = [],
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedSport, setSelectedSport] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedSurface, setSelectedSurface] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [players, setPlayers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeRange, setTimeRange] = useState({ from: null, until: null });
  const [currentView, setCurrentView] = useState("players");
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [isFindBuddyEnabled, setIsFindBuddyEnabled] = useState(false);
  const [playersNeeded, setPlayersNeeded] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [bookingId, setBookingId] = useState(null);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [primaryPlayer, setPrimaryPlayer] = useState(null);
  const [reservationId, setReservationId] = useState(null);
  const [clientSecret, setClientSecret] = useState(null);
  const [paymentIntent, setPaymentIntent] = useState(null);
  const [reservationLoading, setReservationLoading] = useState(false);
  const location = useLocation();
  const { id } = useParams();
  const { club, sports, user_subscription, user_permissions, club_membership } =
    useClub();

  const [clinic, setClinic] = useState(null);
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const { state: authState, dispatch: authDispatch } = useContext(AuthContext);
  const navigate = useNavigate();

  // Get the user's membership plan
  const userMembershipPlan = useMemo(() => {
    if (!user_subscription?.planId || !club_membership?.length) return null;
    return club_membership.find(
      (plan) => plan.plan_id === user_subscription.planId
    );
  }, [user_subscription, club_membership]);

  // Calculate the maximum allowed date based on the user's membership plan
  const maxAllowedDate = useMemo(() => {
    // Check if advance booking is enabled for clinics
    if (userMembershipPlan?.advance_booking_enabled?.clinic === false) {
      // If advance booking is disabled, allow booking without limit
      const farFutureDate = new Date();
      farFutureDate.setFullYear(farFutureDate.getFullYear() + 10); // Set to 10 years in the future
      return farFutureDate;
    }

    // Default to 10 days in advance if no plan or advance booking days are specified
    const advanceDays = userMembershipPlan?.advance_booking_days?.clinic || 10;
    const today = new Date();
    const maxDate = new Date();
    maxDate.setDate(today.getDate() + advanceDays);
    return maxDate;
  }, [userMembershipPlan]);

  const user_id = localStorage.getItem("user");

  const [warningModal, setWarningModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    actionButtonText: "",
    actionButtonLink: "",
    type: "warning",
  });

  const fetchSports = async () => {
    try {
      const clinicResponse = await tdk.getOne("clinics", id, {});
      setClinic(clinicResponse.model);
    } catch (error) {
      console.error(error);
    }
  };
  const fetchPlayers = async () => {
    try {
      const playersResponse = await tdk.getList("user", {
        filter: [`role,cs,user`, `club_id,cs,${club?.id}`],
      });

      setPlayers(playersResponse.list);
    } catch (error) {
      console.error(error);
    }
  };
  const fetchGroups = async () => {
    try {
      const groupsResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/groups",
        {},
        "GET"
      );
      setGroups(groupsResponse.groups);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchFamilyMembers = async () => {
    try {
      const parsedUserId = parseInt(user_id);

      // Don't fetch if we don't have a valid user ID
      if (!user_id || isNaN(parsedUserId)) {
        console.error("Invalid user_id for fetching family members:", user_id);
        return;
      }

      const familyResponse = await tdk.getList("user", {
        filter: [`guardian,eq,${parsedUserId}`, `role,cs,user`],
      });
      setFamilyMembers(familyResponse.list);
    } catch (error) {
      console.error("Error fetching family members:", error);
    }
  };

  useEffect(() => {
    // fetchClub();
    (async () => {
      setIsLoading(true);
      await fetchSports();
      await fetchPlayers();
      await fetchGroups();
      await fetchFamilyMembers();
      setIsLoading(false);
    })();
  }, []);

  useEffect(() => {
    if (club?.id) {
      fetchPlayers();
    }
  }, [club?.id]);

  const togglePlayer = (player) => {
    setSelectedPlayers((prev) => {
      const isSelected = prev.some((p) => p.id === player.id);
      if (isSelected) {
        return prev.filter((p) => p.id !== player.id);
      }
      return [...prev, player];
    });
  };

  const handlePrimaryPlayerChange = (selectedOption) => {
    // Handle both Select component (with .value) and direct user object
    const newPrimaryPlayer = selectedOption.value || selectedOption;

    // Don't do anything if the same player is selected
    if (newPrimaryPlayer?.id === primaryPlayer?.id) {
      return;
    }

    setPrimaryPlayer(newPrimaryPlayer);

    // Update selected players to replace the current primary player with the new one
    setSelectedPlayers((prev) => {
      // Remove the current primary player if they're in the list
      const filteredPlayers = prev.filter((p) => p.id !== primaryPlayer?.id);

      // Check if the new primary player is already in the filtered list
      const isNewPlayerAlreadyInList = filteredPlayers.some(
        (p) => p.id === newPrimaryPlayer.id
      );

      if (isNewPlayerAlreadyInList) {
        // If new player is already in the list, just move them to the front
        const otherPlayers = filteredPlayers.filter(
          (p) => p.id !== newPrimaryPlayer.id
        );
        return [newPrimaryPlayer, ...otherPlayers];
      } else {
        // If new player is not in the list, add them at the beginning
        return [newPrimaryPlayer, ...filteredPlayers];
      }
    });
  };

  // Get the maximum players allowed based on custom request threshold
  const maxPlayersAllowed = getCustomRequestThreshold(
    club?.custom_request_threshold,
    clinic?.sport_id,
    clinic?.type,
    clinic?.sub_type,
    4, // default threshold
    sports
  );

  // Clear selected players when threshold changes and current selection exceeds new limit
  useEffect(() => {
    if (selectedPlayers.length > maxPlayersAllowed) {
      console.log(
        `Clearing selected players: current ${selectedPlayers.length} exceeds new threshold ${maxPlayersAllowed}`
      );
      setSelectedPlayers([]);
      setPlayersNeeded(1); // Reset to minimum

      // Show toast notification to user
      setWarningModal({
        isOpen: true,
        title: "Player Selection Cleared",
        message: `Player selection cleared. New maximum is ${maxPlayersAllowed} players. Please select players again.`,
        type: "warning",
      });
    }
  }, [maxPlayersAllowed]);

  // Initialize primary player with current user
  useEffect(() => {
    if (players.length > 0 && !primaryPlayer) {
      const currentUser = players.find(
        (player) => player.id === parseInt(user_id)
      );
      if (currentUser) {
        setPrimaryPlayer(currentUser);
      }
    }
  }, [players, primaryPlayer, user_id]);

  const fees = calculateClinicFees({
    costPerHead: clinic?.cost_per_head,
    playerCount: selectedPlayers?.length,
    feeSettings: club?.fee_settings,
  });

  const handleNextPayment = async () => {
    if (!user_subscription?.planId) {
      setWarningModal({
        isOpen: true,
        title: "Subscription Required",
        message: "Please subscribe to a membership plan to join clinics",
        actionButtonText: "View Membership Plans",
        actionButtonLink: "/user/membership/buy",
        type: "warning",
      });
      return;
    }

    if (!user_permissions?.allowClinic) {
      setWarningModal({
        isOpen: true,
        title: "Plan Upgrade Required",
        message: `Your current plan (${user_permissions?.planName}) does not include clinic bookings. Please upgrade your plan.`,
        actionButtonText: "Upgrade Plan",
        actionButtonLink: "/user/membership/buy",
        type: "error",
      });
      return;
    }

    // Check if the clinic date is within the allowed range
    const clinicDate = new Date(clinic?.date);
    if (
      clinicDate > maxAllowedDate &&
      userMembershipPlan?.advance_booking_enabled?.clinic !== false
    ) {
      const advanceDays =
        userMembershipPlan?.advance_booking_days?.clinic || 10;
      setWarningModal({
        isOpen: true,
        title: "Date Selection Error",
        message: `Your membership plan only allows booking clinics ${advanceDays} days in advance. Please select a clinic within your allowed date range.`,
        type: "warning",
      });
      return;
    }

    if (!selectedPlayers.length) {
      setWarningModal({
        isOpen: true,
        title: "Players Required",
        message: "Please select at least one player",
        type: "warning",
      });
      return;
    }

    setReservationLoading(true);
    try {
      const paymentIntentResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",
        { amount: fees.total },
        "POST"
      );

      // Check for payment intent creation error
      if (paymentIntentResponse.error) {
        throw new Error(
          paymentIntentResponse.message || "Failed to create payment intent"
        );
      }

      const payload = {
        sport_id: clinic?.sport_id,
        type: clinic?.type,
        sub_type: clinic?.sub_type,
        date: clinic?.date,
        start_time: clinic?.start_time,
        end_time: clinic?.end_time,
        duration: clinic?.duration,
        price: fees.total,
        clinic_fee: fees.clinicFee,
        service_fee: fees.serviceFee,
        player_ids: selectedPlayers.map((p) => p.id),
        primary_player_id: primaryPlayer?.id || parseInt(user_id), // Add primary player ID
        buddy_details: null,
        payment_status: 0,
        payment_intent: paymentIntentResponse.payment_intent,
        reservation_type: reservationTypes.clinic,
        clinic_id: id,
      };

      const reservationResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/user/reservations",
        payload,
        "POST"
      );

      // Create activity log
      sdk.setTable("activity_logs");
      const activityResponse = await sdk.callRestAPI(
        {
          activity_type: activityLogTypes.clinic,
          user_id: user_id,
          club_id: club?.id,
          action_type: actionLogTypes.CREATE,
          data: JSON.stringify(payload),
          description: "Created a clinic reservation",
        },
        "POST"
      );

      setClientSecret(paymentIntentResponse.client_secret);
      setPaymentIntent(paymentIntentResponse.payment_intent);
      setReservationId(reservationResponse.reservation_id);
      setBookingId(reservationResponse.booking_id);
      setCurrentStep(2);
    } catch (error) {
      console.error(error);
      setWarningModal({
        isOpen: true,
        title: "Reservation Error",
        message: error.message || "Error creating clinic reservation",
        type: "error",
      });
    } finally {
      setReservationLoading(false);
    }
  };

  React.useEffect(() => {
    updateBrowserTab({
      path: `/user/clinic-booking/${id}`,
      clubName: club?.name,
      favicon: club?.club_logo,
      description: `Join ${clinic?.name}`,
    });
  }, [club?.club_logo]);

  const clinicReservationDescription = club?.clinic_description
    ? JSON.parse(club?.clinic_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  // Check if user has permission to access clinic bookings
  if (user_permissions && !user_permissions.allowClinic) {
    return (
      <AccessRestricted
        message={`Your current plan (${user_permissions?.planName}) does not include clinic bookings. Please upgrade your plan to access this feature.`}
      />
    );
  }

  return (
    <div className="h-full">
      <WarningModal
        isOpen={warningModal.isOpen}
        onClose={() => setWarningModal({ ...warningModal, isOpen: false })}
        title={warningModal.title}
        message={warningModal.message}
        actionButtonText={warningModal.actionButtonText}
        actionButtonLink={warningModal.actionButtonLink}
        type={warningModal.type}
      />
      {isLoading && <LoadingSpinner />}

      <div className="">
        <div className="flex items-center justify-center bg-white p-4">
          {currentView === "players" && <div className=" ">Clinic Booking</div>}
        </div>
        <div className="p-4">
          <BackButton
            onBack={() => {
              navigate(-1);
            }}
          />
          {currentStep === 1 && (
            <div className="mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3">
              <div className="space-y-4">
                {userMembershipPlan?.advance_booking_enabled?.clinic ===
                false ? (
                  <div className="rounded-lg bg-blue-50 p-3 text-sm text-blue-700">
                    You can sign up for a clinic for any future date.
                  </div>
                ) : (
                  userMembershipPlan?.advance_booking_days?.clinic !==
                    undefined && (
                    <div className="rounded-lg bg-blue-50 p-3 text-sm text-blue-700">
                      You can sign up for a clinic up to{" "}
                      {userMembershipPlan?.advance_booking_days?.clinic}{" "}
                      {userMembershipPlan?.advance_booking_days?.clinic === 1
                        ? "day"
                        : "days"}{" "}
                      in advance.
                    </div>
                  )
                )}
                <ClinicReservationSummary
                  selectedSport={clinic?.sport_id}
                  sports={sports}
                  selectedType={clinic?.type}
                  selectedSubType={clinic?.sub_type}
                  selectedDate={clinic?.date}
                  selectedTimes={clinic?.start_time}
                  clinic={{ ...clinic, ...location?.state?.clinic }}
                />
              </div>

              <div className="space-y-4">
                <AddPlayers
                  players={players}
                  groups={groups}
                  selectedPlayers={selectedPlayers}
                  onPlayerToggle={togglePlayer}
                  isFindBuddyEnabled={isFindBuddyEnabled}
                  onFindBuddyToggle={() =>
                    setIsFindBuddyEnabled(!isFindBuddyEnabled)
                  }
                  playersNeeded={playersNeeded}
                  onPlayersNeededChange={setPlayersNeeded}
                  showPlayersNeeded={false}
                  showCurrentGroup={false}
                  setSelectedPlayers={setSelectedPlayers}
                  maximumPlayers={Math.min(
                    maxPlayersAllowed,
                    location?.state?.clinic?.slots_remaining
                      ? parseInt(location?.state?.clinic?.slots_remaining)
                      : maxPlayersAllowed
                  )}
                  familyMembers={familyMembers}
                  currentUser={primaryPlayer}
                  onCurrentUserChange={handlePrimaryPlayerChange}
                  userProfile={players.find((p) => p.id === parseInt(user_id))}
                />
              </div>
              <div className="h-fit rounded-lg bg-white shadow-5">
                <div className="rounded-lg bg-gray-50 p-4 text-center">
                  <h2 className="text-base font-medium">Reserving details</h2>
                </div>
                <div className="p-4">
                  <div className="space-y-6">
                    {/* Players count */}
                    <div>
                      <h3 className="text-sm text-gray-500">
                        PLAYERS ({selectedPlayers.length})
                      </h3>
                      <div className="mt-1">
                        {selectedPlayers.map((player) => (
                          <div key={player.id} className="text-sm">
                            {player.first_name} {player.last_name}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Fees section */}
                    <div>
                      <h3 className="text-sm text-gray-500">FEES</h3>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="flex items-center gap-1">
                          Clinic Fee
                          <span className="text-xs text-gray-500">
                            ({fCurrency(clinic?.cost_per_head)} ×{" "}
                            {selectedPlayers.length} players)
                          </span>
                        </span>
                        <span>{fCurrency(fees.clinicFee)}</span>
                      </div>
                      <div className="mt-2 flex items-center justify-between">
                        <span>Service Fee</span>
                        <span>{fCurrency(fees.serviceFee)}</span>
                      </div>
                    </div>

                    {/* Total */}
                    <div className="flex items-center justify-between border-t pt-4">
                      <span>Total</span>
                      <span className="font-medium">
                        {fCurrency(fees.total)}
                      </span>
                    </div>

                    {/* Notification message */}
                    <div className="rounded-lg bg-[#F17B2C] p-3 text-sm text-white">
                      <div className="flex items-start gap-2">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z"
                            fill="white"
                          />
                        </svg>

                        <span>
                          After reserving, you will have 15 minutes to make the
                          payment.
                        </span>
                      </div>
                    </div>

                    {/* Make reservation button */}
                    <InteractiveButton
                      loading={reservationLoading}
                      onClick={handleNextPayment}
                      className="w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90"
                    >
                      <div className="flex flex-col items-center">
                        <span>Reserve Now</span>
                        <span className="text-sm opacity-80">
                          and continue to payment
                        </span>
                      </div>
                    </InteractiveButton>
                    <p className="text-center text-sm text-gray-500">
                      {clinicReservationDescription.reservation_description}
                    </p>

                    {/* Additional note */}
                    <div className="space-y-2 text-center text-sm text-gray-500">
                      <p>(You will not be charged yet)</p>
                      <p>
                        For any issues, please contact our support team at{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="font-medium underline"
                        >
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {currentStep === 2 && (
            <div className="mx-auto max-w-6xl">
              <div className="rounded-xl bg-[#F17B2C] px-4 py-3 text-white">
                <div className="flex items-center gap-2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span className="!text-sm">
                    Your session is reserved. You have 15 minutes to complete
                    the payment, otherwise the reservation will be canceled.
                  </span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="space-y-6">
                  <ClinicReservationSummary
                    selectedSport={clinic?.sport_id}
                    sports={sports}
                    selectedType={clinic?.type}
                    selectedSubType={clinic?.sub_type}
                    selectedDate={clinic?.date}
                    selectedTimes={clinic?.start_time}
                    clinic={{ ...clinic, ...location?.state?.clinic }}
                  />
                </div>

                <div className="space-y-6">
                  <div className="rounded-xl bg-white p-6 shadow-5">
                    <h2 className="mb-4 text-center text-lg font-medium">
                      Payment details
                    </h2>
                    <div className="space-y-4">
                      {/* <div className="flex items-center justify-between">
                        <span className="text-gray-500">Base fee</span>
                        <span>
                          {fCurrency(fee * (selectedPlayers?.length || 1))}
                        </span>
                      </div> */}
                      {/* Players count */}
                      <div>
                        <h3 className="text-sm text-gray-500">
                          PLAYERS ({selectedPlayers.length})
                        </h3>
                        <div className="mt-1">
                          {selectedPlayers.map((player) => (
                            <div key={player.id} className="text-sm">
                              {player.first_name} {player.last_name}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Fees section */}
                      <div>
                        <h3 className="text-sm text-gray-500">FEES</h3>
                        <div className="mt-2 flex items-center justify-between">
                          <span className="flex items-center gap-1">
                            Clinic Fee
                            <span className="text-xs text-gray-500">
                              ({fCurrency(clinic?.cost_per_head)} ×{" "}
                              {selectedPlayers.length} players)
                            </span>
                          </span>
                          <span>{fCurrency(fees.clinicFee)}</span>
                        </div>
                        <div className="mt-2 flex items-center justify-between">
                          <span>Service Fee</span>
                          <span>{fCurrency(fees.serviceFee)}</span>
                        </div>
                      </div>

                      {/* Total */}
                      <div className="flex items-center justify-between border-t pt-4">
                        <span>Total</span>
                        <span className="font-medium">
                          {fCurrency(fees.total)}
                        </span>
                      </div>
                      <div>
                        <CheckoutForm
                          user={userProfile}
                          bookingId={bookingId}
                          reservationId={reservationId}
                          clientSecret={clientSecret}
                          paymentIntent={paymentIntent}
                          navigateRoute={`/user/payment-success/${reservationId}?type=clinic`}
                        />
                        <p className="mt-5 text-center text-sm text-gray-500">
                          {clinicReservationDescription.payment_description}
                        </p>
                      </div>

                      <div className="space-y-4 text-sm text-gray-500">
                        <p>
                          By clicking "Pay now" you agree to our{" "}
                          <Link
                            to="/terms-and-conditions"
                            target="_blank"
                            className="font-medium underline"
                          >
                            Terms and Conditions
                          </Link>{" "}
                          and{" "}
                          <Link
                            to="/privacy-policy"
                            target="_blank"
                            className="font-medium underline"
                          >
                            Privacy Policy
                          </Link>
                          . All sales are final unless stated otherwise.
                        </p>
                        <p>
                          For any issues, please contact our support team at{" "}
                          <a
                            href="mailto:<EMAIL>"
                            className="font-medium underline"
                          >
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClinicBooking;
