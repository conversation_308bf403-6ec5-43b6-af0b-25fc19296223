import MembershipCard from "Components/MembershipCard";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import MkdSDK from "Utils/MkdSDK";
import { BackButton } from "Components/BackButton";
import { useNavigate, useSearchParams } from "react-router-dom";
import { showToast, GlobalContext } from "Context/Global";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import { getCardIcon } from "Utils/cardIcons";
import { updateBrowserTab } from "Utils/utils";

let sdk = new MkdSDK();

export default function UserMembership() {
  const [membershipPlans, setMembershipPlans] = useState([]);
  const [plansLoading, setPlansLoading] = useState(true);
  const [cardsLoading, setCardsLoading] = useState(false);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [defaultCard, setDefaultCard] = useState(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const user_id = localStorage.getItem("user");
  const [userClubResponse, setUserClubResponse] = useState(null);
  const [activeSubscription, setActiveSubscription] = useState(null);

  // Family member support
  const familyMemberId = searchParams.get("familyMemberId");
  const familyMemberName = searchParams.get("familyMemberName");
  const isFamilyMemberSwitch = authState.isFamilyMemberSwitch;
  const familyMemberDetails = authState.familyMemberDetails;

  // Determine the target user ID - prioritize auth state over URL params
  const targetUserId = useMemo(() => {
    if (isFamilyMemberSwitch && authState.user) {
      return authState.user;
    }
    return familyMemberId || user_id;
  }, [isFamilyMemberSwitch, authState.user, familyMemberId, user_id]);

  // Get the display name for the current context - prioritize auth state
  const getDisplayName = useCallback(() => {
    if (isFamilyMemberSwitch && familyMemberDetails) {
      return familyMemberDetails.first_name;
    }
    if (familyMemberName) {
      return familyMemberName;
    }
    return "Your";
  }, [isFamilyMemberSwitch, familyMemberDetails, familyMemberName]);

  // Get the full display name for headers
  const getFullDisplayName = useCallback(() => {
    if (isFamilyMemberSwitch && familyMemberDetails) {
      return `${familyMemberDetails.first_name} ${familyMemberDetails.last_name}`;
    }
    if (familyMemberName) {
      return familyMemberName;
    }
    return "Your";
  }, [isFamilyMemberSwitch, familyMemberDetails, familyMemberName]);

  const getMembershipPlans = useCallback(async () => {
    setPlansLoading(true);
    try {
      sdk.setTable("user");
      const userResponse = await sdk.callRestAPI({ id: user_id }, "GET");

      sdk.setTable("clubs");
      const userClubResponse = await sdk.callRestAPI(
        { id: userResponse?.model?.club_id },
        "GET"
      );
      setUserClubResponse(userClubResponse.model);
      console.log(
        "view model response",
        userClubResponse?.model?.membership_settings
      );
      setMembershipPlans(
        JSON.parse(userClubResponse?.model?.membership_settings) || []
      );
    } catch (e) {
      console.log(e);
    } finally {
      setPlansLoading(false);
    }
  }, [user_id]);

  const getCards = useCallback(async () => {
    try {
      setCardsLoading(true);
      const {
        data: cards,
        error,
        message,
      } = await sdk.getCustomerStripeCards();
      console.log(cards);
      if (error) {
        showToast(globalDispatch, message, 5000);
      }
      if (!cards) {
        return;
      }

      const defaultCard = cards?.data?.find(
        (card) => card.id === cards?.data[0]?.customer?.default_source
      );
      setDefaultCard(defaultCard);
    } catch (error) {
      console.error("ERROR", error);
      showToast(globalDispatch, error.message, 5000);
      tokenExpireError(dispatch, error.code);
    } finally {
      setCardsLoading(false);
    }
  }, [globalDispatch, dispatch]);

  useEffect(() => {
    getMembershipPlans();
    getCards();
  }, [getMembershipPlans, getCards]);

  // Separate useEffect for browser tab updates to avoid dependency loops
  useEffect(() => {
    if (userClubResponse) {
      const displayName = getFullDisplayName();
      const isForFamilyMember = isFamilyMemberSwitch || familyMemberId;

      updateBrowserTab({
        title: isForFamilyMember
          ? `Membership for ${displayName}`
          : "Membership",
        path: "/user/membership",
        clubName: userClubResponse?.name,
        favicon: userClubResponse?.club_logo,
        description: isForFamilyMember
          ? `Membership for ${displayName}`
          : "Membership",
      });
    }
  }, [
    isFamilyMemberSwitch,
    familyMemberId,
    getFullDisplayName,
    userClubResponse?.name,
    userClubResponse?.club_logo,
  ]);

  const handleSelect = (plan) => {
    setSelectedPlan(plan);
    setCurrentStep(2);
  };

  const handlePayment = async () => {
    setSubscriptionLoading(true);
    try {
      if (activeSubscription?.subId) {
        const data = await sdk.changeStripeSubscription({
          userId: targetUserId,
          activeSubscriptionId: activeSubscription.subId,
          newPlanId: selectedPlan.plan_id,
        });
        if (data.error) {
          console.error(data.message);
          showToast(globalDispatch, data.message, 7500, "error");
        } else {
          showToast(globalDispatch, "Subscription updated successfully", 3000);
          navigate("/user/profile?tab=membership");
        }
      } else {
        const data = await sdk.createStripeSubscription({
          planId: selectedPlan.plan_id,
          userId: targetUserId, // Add userId for family member subscriptions
        });
        if (data.error) {
          console.error(data.message);
          showToast(globalDispatch, data.message, 7500, "error");
        } else {
          showToast(globalDispatch, "Subscription created successfully", 3000);
          navigate("/user/profile?tab=membership");
        }
      }
    } catch (error) {
      console.error("Error", error);
      showToast(globalDispatch, error.message, 7500, "error");
      tokenExpireError(globalDispatch, error.code);
    } finally {
      setSubscriptionLoading(false);
    }
  };

  const getActiveSubscription = useCallback(async () => {
    try {
      const data = await sdk.getCustomerStripeSubscription(targetUserId);
      setActiveSubscription(data.customer);
    } catch (error) {
      console.error(error);
      tokenExpireError(dispatch, error.code);
    }
  }, [targetUserId, dispatch]);

  useEffect(() => {
    getActiveSubscription();
  }, [getActiveSubscription]);
  // Loading skeleton component for membership cards
  const MembershipCardSkeleton = () => (
    <div className="h-fit animate-pulse rounded-xl border border-gray-200 bg-white p-5 shadow-sm">
      <div className="mb-3 flex items-center justify-between border-b border-gray-100 pb-4">
        <div className="h-6 w-32 rounded bg-gray-200"></div>
        <div className="h-6 w-16 rounded bg-gray-200"></div>
      </div>
      <div className="mb-4">
        <div className="mb-2 h-8 w-24 rounded bg-gray-200"></div>
        <div className="h-4 w-16 rounded bg-gray-200"></div>
      </div>
      <div className="mb-4 rounded-lg bg-gray-50 p-3">
        <div className="mb-2 h-5 w-40 rounded bg-gray-200"></div>
        <div className="grid grid-cols-2 gap-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-4 rounded bg-gray-200"></div>
          ))}
        </div>
      </div>
      <div className="mb-4 space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center gap-2">
            <div className="h-5 w-5 rounded bg-gray-200"></div>
            <div className="h-4 flex-1 rounded bg-gray-200"></div>
          </div>
        ))}
      </div>
      <div className="h-10 w-full rounded-lg bg-gray-200"></div>
    </div>
  );

  return (
    <div className="">
      <div className="p-4">
        <BackButton
          onBack={() => {
            if (currentStep === 1) {
              navigate(-1);
            } else {
              setCurrentStep(1);
            }
          }}
        />
      </div>

      {currentStep === 1 && (
        <div className="p-4">
          <div className="space-y-6">
            <div className="mx-auto max-w-7xl p-4">
              {/* Header Section */}
              <div className="mb-8 text-center">
                <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
                  {isFamilyMemberSwitch || familyMemberId
                    ? `Choose Membership for ${getFullDisplayName()}`
                    : "Choose Your Membership"}
                </h1>
                <p className="mx-auto mt-4 max-w-2xl text-gray-600">
                  {isFamilyMemberSwitch || familyMemberId
                    ? `Select the perfect plan for ${getDisplayName()} and start enjoying all the benefits`
                    : "Select the perfect plan that fits your needs and start enjoying all the benefits"}
                </p>
              </div>

              {/* Membership Plans Grid */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {plansLoading ? (
                  // Show loading skeletons
                  Array.from({ length: 3 }).map((_, index) => (
                    <MembershipCardSkeleton key={index} />
                  ))
                ) : membershipPlans?.length > 0 ? (
                  // Show actual membership cards
                  membershipPlans.map((plan) => (
                    <div
                      key={plan.plan_name}
                      className="transform transition-all duration-200 hover:scale-105"
                    >
                      <MembershipCard
                        {...plan}
                        isCurrentPlan={currentPlan === plan.id}
                        onSelect={() => handleSelect(plan)}
                        isActive={activeSubscription?.planId === plan.plan_id}
                      />
                    </div>
                  ))
                ) : (
                  // Show empty state
                  <div className="col-span-full py-12 text-center">
                    <div className="mx-auto h-24 w-24 text-gray-400">
                      <svg
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">
                      No membership plans available
                    </h3>
                    <p className="mt-2 text-gray-500">
                      Please contact support for assistance.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      {currentStep === 2 && (
        <div className="p-4">
          <div className="space-y-6">
            <div className="mx-auto max-w-7xl p-4">
              {/* Header Section */}
              <div className="mb-8 text-center">
                <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
                  {selectedPlan?.price === 0
                    ? isFamilyMemberSwitch || familyMemberId
                      ? `Confirm Selection for ${getFullDisplayName()}`
                      : "Confirm Your Selection"
                    : isFamilyMemberSwitch || familyMemberId
                    ? `Complete Purchase for ${getFullDisplayName()}`
                    : "Complete Your Purchase"}
                </h1>
                <p className="mt-4 text-gray-600">
                  {selectedPlan?.price === 0
                    ? isFamilyMemberSwitch || familyMemberId
                      ? `Review the free plan selection for ${getDisplayName()} and activate the membership`
                      : "Review your free plan selection and activate your membership"
                    : isFamilyMemberSwitch || familyMemberId
                    ? `Review the selection and payment details for ${getDisplayName()}`
                    : "Review your selection and payment details"}
                </p>
              </div>

              {/* Payment Section */}
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {/* Membership Details Card */}
                <div className="order-2 lg:order-1">
                  <div className="overflow-hidden rounded-xl bg-white shadow-5">
                    <div className="bg-primaryBlue px-6 py-4">
                      <h2 className="text-lg font-semibold text-white">
                        Membership Details
                      </h2>
                    </div>
                    <div className="p-6">
                      <div className="mb-6">
                        <h3 className="mb-2 text-2xl font-bold text-gray-900">
                          {selectedPlan?.plan_name}
                        </h3>
                        <div className="text-3xl font-bold text-primaryBlue">
                          {fCurrency(selectedPlan?.price)}
                          <span className="ml-1 text-lg font-normal text-gray-500">
                            /month
                          </span>
                        </div>
                      </div>

                      <div className="border-t border-gray-200 pt-6">
                        <h4 className="mb-4 font-semibold text-gray-900">
                          Features included:
                        </h4>
                        <div className="space-y-3">
                          {selectedPlan?.features?.map((item, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-3"
                            >
                              <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-green-100">
                                <svg
                                  className="h-4 w-4 text-green-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              </div>
                              <span className="text-gray-700">{item.text}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Details Card */}
                <div className="order-1 lg:order-2">
                  <div className="overflow-hidden rounded-xl bg-white shadow-5">
                    <div className="bg-primaryGreen px-6 py-4">
                      <h2 className="text-lg font-semibold text-white">
                        {selectedPlan?.price === 0
                          ? "Subscription Details"
                          : "Payment Details"}
                      </h2>
                    </div>

                    <div className="p-6">
                      {/* Pricing Breakdown */}
                      <div className="mb-8">
                        <div className="space-y-4">
                          <div className="flex justify-between text-sm uppercase tracking-wide text-gray-600">
                            <span>Billing Summary</span>
                          </div>
                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-gray-700">Plan price</span>
                              <span className="font-medium">
                                {fCurrency(selectedPlan?.price)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-700">
                                Taxes & fees
                              </span>
                              <span className="font-medium">
                                {fCurrency(0)}
                              </span>
                            </div>
                          </div>
                          <div className="border-t border-gray-200 pt-4">
                            <div className="flex justify-between text-lg font-bold">
                              <span>Total</span>
                              <span className="text-primaryBlue">
                                {fCurrency(selectedPlan?.price)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Payment Method - Show for all plans */}
                      <div className="mb-6">
                        <h4 className="mb-4 font-semibold text-gray-900">
                          Payment Method
                        </h4>
                        {cardsLoading ? (
                          <div className="animate-pulse">
                            <div className="h-16 rounded-lg bg-gray-200"></div>
                          </div>
                        ) : defaultCard ? (
                          <div className="flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4">
                            <div className="flex items-center space-x-3">
                              {getCardIcon(defaultCard?.brand)}
                              <div>
                                <p className="font-medium text-gray-900">
                                  {defaultCard?.brand?.toUpperCase()} ••••{" "}
                                  {defaultCard?.last4}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Default payment method
                                </p>
                              </div>
                            </div>
                            <button
                              onClick={() =>
                                navigate("/user/profile?tab=payment-methods")
                              }
                              className="text-sm font-medium text-primaryBlue transition-colors hover:text-blue-700"
                            >
                              Change
                            </button>
                          </div>
                        ) : (
                          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0">
                                <svg
                                  className="h-5 w-5 text-red-400"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                                  />
                                </svg>
                              </div>
                              <div>
                                <p className="mb-2 text-sm font-medium text-red-800">
                                  Payment method required
                                </p>
                                <p className="mb-3 text-sm text-red-700">
                                  Please add a payment method to continue with
                                  your subscription.
                                </p>
                                <button
                                  onClick={() =>
                                    navigate(
                                      "/user/profile?tab=payment-methods"
                                    )
                                  }
                                  className="text-sm font-medium text-red-600 underline transition-colors hover:text-red-700"
                                >
                                  Add Payment Method →
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Action Button */}
                      <InteractiveButton
                        onClick={handlePayment}
                        loading={subscriptionLoading}
                        disabled={!defaultCard}
                        className={`w-full rounded-lg py-3 text-lg font-semibold transition-all duration-200 ${
                          defaultCard
                            ? "bg-primaryBlue text-white hover:bg-blue-700"
                            : "cursor-not-allowed bg-gray-300 text-gray-500"
                        }`}
                      >
                        {selectedPlan?.price === 0
                          ? "Activate Free Plan"
                          : defaultCard
                          ? "Complete Purchase"
                          : "Add payment method to continue"}
                      </InteractiveButton>

                      {/* Terms */}
                      <p className="mt-6 text-center text-xs leading-relaxed text-gray-500">
                        By completing this purchase, you agree to our Terms of
                        Service and Privacy Policy. Your subscription will
                        automatically renew monthly unless cancelled.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
