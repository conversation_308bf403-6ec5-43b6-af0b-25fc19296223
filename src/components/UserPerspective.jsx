import { useState } from "react";
import BottomDrawer from "./Drawers/BottomDrawer";
import UserSignupPerspective from "./Perspectives/UserSignupPerspective";
import MembershipPerspective from "./Perspectives/MembershipPerspective";
import SplashScreenPagePreview from "./ProfileSetUp/SplashScreenPagePreview";
import SportTypeSelection from "./Shared/SportTypeSelection";
import HomeLoggedInPerspective from "./Perspectives/HomeLoggedInPerspective";
import CourtBookingPerspective from "./Perspectives/CourtBookingPerspective";
import LessonBookingPerspective from "./Perspectives/LessonBookingPerspective";
import FindBuddyPerspective from "./Perspectives/FindBuddyPerspective";
import ClinicPerspective from "./Perspectives/ClinicPerspective";
import PricesPerspective from "./Perspectives/PricesPerspective";
import CustomRequestThresholdPerspective from "./Perspectives/CustomRequestThresholdPerspective";

export default function UserPerspective({
  club,
  sports,
  pricing,
  courts,
  isOpen,
  onClose,
}) {
  const [activeTab, setActiveTab] = useState("Sports");

  const formatImageList = (splashScreen) => {
    try {
      // First parse the splash_screen JSON string
      const parsedSplashScreen = JSON.parse(splashScreen || "{}");
      // Get the images array from the parsed splash screen
      const images = parsedSplashScreen.images || [];

      // Create array of 9 nulls
      let formattedImages = new Array(9).fill(null);

      // Fill in the images where they exist
      images.forEach((image, index) => {
        if (image && image.url) {
          formattedImages[index] = {
            url: image.url,
            isDefault: true,
            id: image.id || `default-${index}`,
            type: image.type || "image",
          };
        }
      });

      return formattedImages;
    } catch (error) {
      console.error("Error parsing splash screen:", error);
      return new Array(9).fill(null);
    }
  };

  const handleSportSelectionChange = ({ sport, type, subType }) => {
    // Preview only - no functionality needed
    console.log({ sport, type, subType });
  };

  const menuItems = [
    { title: "Sports", path: "/sports" },
    { title: "Prices", path: "/prices" },
    { title: "Home splash screen", path: "/home-splash" },
    { title: "Home_Logged-in", path: "/home-logged-in" },
    { title: "Court booking description", path: "/court-booking" },
    { title: "Lesson booking description", path: "/lesson-booking" },
    { title: "Find a Buddy booking description", path: "/find-buddy" },
    { title: "Clinic description", path: "/clinic" },
    { title: "Custom request threshold", path: "/custom-request" },
    { title: "Membership and modules", path: "/membership" },
    { title: "Custom signup form", path: "/custom-signup-form" },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "Sports":
        return (
          <div className="mx-auto max-w-xl">
            <SportTypeSelection
              sports={sports}
              onSelectionChange={handleSportSelectionChange}
              isChildren={false}
            />
          </div>
        );
      case "Custom signup form":
        return <UserSignupPerspective club={club} />;
      case "Membership and modules":
        return <MembershipPerspective club={club} />;
      case "Home splash screen":
        const parsedSplashScreen = JSON.parse(club?.splash_screen || "{}");
        return (
          <div className="h-full">
            <SplashScreenPagePreview
              clubName={club?.name}
              description={parsedSplashScreen?.bio}
              imageList={formatImageList(club?.splash_screen)}
              clubLogo={club?.club_logo}
              slideshowDelay={parsedSplashScreen?.slideshow_delay || 5000}
            />
          </div>
        );
      case "Home_Logged-in":
        return <HomeLoggedInPerspective club={club} />;
      case "Court booking description":
        return <CourtBookingPerspective club={club} />;
      case "Lesson booking description":
        return <LessonBookingPerspective club={club} />;
      case "Find a Buddy booking description":
        return <FindBuddyPerspective />;
      case "Clinic description":
        return <ClinicPerspective club={club} />;
      case "Prices":
        return <PricesPerspective pricing={pricing} sports={sports} />;
      case "Custom request threshold":
        return <CustomRequestThresholdPerspective club={club} />;
      default:
        return (
          <div>
            <h2 className="mb-2 text-lg font-semibold">{activeTab}</h2>
            <p className="text-gray-600">
              Content for {activeTab} will be displayed here
            </p>
          </div>
        );
    }
  };

  return (
    <BottomDrawer isOpen={isOpen} onClose={onClose} title={"Preview"}>
      <div className="flex h-full flex-col">
        <h1 className="mb-4 text-xl font-bold">{club?.name}</h1>

        {/* Scrollable Tab List */}
        <div className="relative">
          <div className="scrollbar-hide overflow-x-auto">
            <div className="flex space-x-4 border-b border-gray-200 px-4">
              {menuItems.map((item) => (
                <button
                  key={item.title}
                  onClick={() => setActiveTab(item.title)}
                  className={`whitespace-nowrap px-1 py-2 text-sm transition-colors duration-200 ${
                    activeTab === item.title
                      ? "border-b-2 border-blue-600 font-medium text-blue-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {item.title}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-3 flex-1 overflow-y-auto bg-white p-4">
          {renderTabContent()}
        </div>
      </div>
    </BottomDrawer>
  );
}
