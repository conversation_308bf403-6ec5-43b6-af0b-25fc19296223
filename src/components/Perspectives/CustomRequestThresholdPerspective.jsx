export default function CustomRequestThresholdPerspective({ club }) {
  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-amber-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-amber-900">
          Custom Request Threshold
        </h3>
        <p className="text-amber-800">
          Configure the minimum time requirements and thresholds for custom
          lesson requests. This helps manage coach availability and ensures
          adequate notice for scheduling.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Request Requirements
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-amber-500"></div>
              <span>Minimum advance notice period</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-amber-500"></div>
              <span>Maximum booking window</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-amber-500"></div>
              <span>Required request details</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-amber-500"></div>
              <span>Coach response timeframe</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Threshold Benefits
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Better coach availability</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Improved scheduling efficiency</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Reduced last-minute cancellations</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Enhanced user experience</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-3 font-medium text-gray-900">
          How Custom Requests Work
        </h4>
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
              1
            </div>
            <div>
              <h5 className="font-medium text-gray-900">Submit Request</h5>
              <p className="text-sm text-gray-600">
                Users submit detailed lesson requests with preferred times,
                skill level, and specific requirements.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
              2
            </div>
            <div>
              <h5 className="font-medium text-gray-900">Coach Review</h5>
              <p className="text-sm text-gray-600">
                Available coaches review requests and respond with availability
                and pricing within the threshold timeframe.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
              3
            </div>
            <div>
              <h5 className="font-medium text-gray-900">Selection & Booking</h5>
              <p className="text-sm text-gray-600">
                Users compare responses and select their preferred coach and
                time slot to complete the booking.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-gray-50 p-4">
        <h4 className="mb-3 font-medium text-gray-900">
          Current Threshold Settings
        </h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Minimum advance notice:</span>
              <span className="font-medium">24 hours</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Maximum booking window:</span>
              <span className="font-medium">30 days</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Coach response time:</span>
              <span className="font-medium">12 hours</span>
            </div>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Request expiry:</span>
              <span className="font-medium">48 hours</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Minimum lesson duration:</span>
              <span className="font-medium">1 hour</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Maximum participants:</span>
              <span className="font-medium">4 players</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-blue-50 p-4">
        <h4 className="mb-2 font-medium text-blue-900">Important Notes</h4>
        <div className="space-y-2 text-sm text-blue-800">
          <p>
            • Custom requests require more advance notice than regular bookings
          </p>
          <p>
            • Coaches may charge different rates for custom lesson requests
          </p>
          <p>
            • Popular time slots may have higher demand and limited availability
          </p>
          <p>
            • Emergency or same-day requests may be accommodated at coach discretion
          </p>
        </div>
      </div>
    </div>
  );
}
